<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" class="multi-line-form">
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter="handleQuery"
          class="fixed-width-input"
        />
      </el-form-item>
      <el-form-item label="仓库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择仓库"
          clearable
          class="fixed-width-input"
        >
          <el-option
            v-for="item in warehouseOptions"
            :key="item.id"
            :label="item.address"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select
          v-model="queryParams.orderStatus"
          placeholder="请选择订单状态"
          clearable
          class="fixed-width-input"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="下单开始日期" prop="startDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="选择开始日期"
          value-format="YYYY-MM-DD"
          class="fixed-width-input"
        />
      </el-form-item>
      <el-form-item label="下单结束日期" prop="endDate">
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="选择结束日期"
          value-format="YYYY-MM-DD"
          class="fixed-width-input"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="Download" @click="handleExport" :loading="exportLoading">导出</el-button>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="orderList">
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="订单编号" align="center" width="180" prop="orderCode" />
      <el-table-column label="用户名" align="center" width="180" prop="createUser" />
      <el-table-column label="订单状态" align="center" width="100" prop="orderStatus">
        <template #default="scope">
          <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
            {{ formatOrderStatus(scope.row.orderStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总价格(元)" align="center" prop="totalPrice">
        <template #default="scope">
          <span>{{ formatPrice(scope.row.totalPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总重量（公斤）" align="center" prop="totalWeight" />
      <el-table-column label="仓库名称" align="center" width="180" prop="warehouseName" :show-overflow-tooltip="true" />
      <el-table-column label="下单时间" align="center" prop="orderTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.orderTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.payTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="approveUser" />
      <el-table-column label="审核时间" align="center" prop="approveTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.approveTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" align="center" prop="updateUser" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdate(scope.row, true)"
            v-hasPermi="['system:declareOrder:edit']"
          >修改</el-button>
          <el-button
            type="primary"
            link
            icon="View"
            @click="handleUpdate(scope.row, false)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 引入修改申报订单对话框组件 -->
    <EditDialog
      v-model:visible="open"
      :order-id="orderId"
      :order-code="orderCode"
      :is-edit="isEdit"
      @success="getList"
    />
  </div>
</template>

<script setup name="DeclareOrder">
import { ref, onMounted, getCurrentInstance } from 'vue'
import EditDialog from './components/EditDialog.vue'  
import { listDeclareOrder, getWarehouseList, orderListExcel } from "@/api/system/declareOrder"
import { ElMessage } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance();
// 遮罩层
const loading = ref(false);
// 详情加载遮罩
const detailLoading = ref(false);
// 提交按钮加载状态
const submitLoading = ref(false);
// 导出按钮加载状态
const exportLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 申报订单表格数据
const orderList = ref([]);
// 仓库选项
const warehouseOptions = ref([]);
// 是否显示弹出层
const open = ref(false);
// 订单编号
const orderCode = ref('');
// 订单ID
const orderId = ref(null);
// 订单详情数据
const orderDetailData = ref([]);
// 是否是编辑状态
const isEdit = ref(false);

// 订单状态选项
const statusOptions = ref([
  { value: "1001", label: "待审核" },
  { value: "1002", label: "待支付" },
  { value: "1003", label: "待收货" },
  { value: "1004", label: "已完成" }
]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  startDate: undefined,
  endDate: undefined,
  warehouseId: undefined,
  orderStatus: undefined,  
  userName: undefined
});

// 格式化订单状态
function formatOrderStatus(status) {
  const statusMap = {
    '1001': '待审核',
    '1002': '待支付',
    '1003': '待收货',
    '1004': '已完成'
  };
  return statusMap[status] || status;
}

// 获取订单状态对应的标签类型
function getOrderStatusType(status) {
  const typeMap = {
    '1001': 'warning',
    '1002': 'info',
    '1003': 'primary',
    '1004': 'success'
  };
  return typeMap[status] || '';
}

// 格式化价格（分转元）
function formatPrice(price) {
  if (price === undefined || price === null) {
    return '0.00';
  }
  return (price / 100).toFixed(2);
}

/** 查询申报订单列表 */
function getList() {
  loading.value = true;
  const params = {
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate,
    warehouseId: queryParams.value.warehouseId,
    orderStatus: queryParams.value.orderStatus, // 添加订单状态参数
    userName: queryParams.value.userName
  };

  listDeclareOrder(params).then(response => {
    if (response.code === "200") {
      orderList.value = response.content.list || [];
      total.value = response.content.total || 0;
    } else {
      orderList.value = [];
      total.value = 0;
      ElMessage.error(response.msg || "获取申报订单列表失败");
    }
    loading.value = false;
  }).catch(error => {
    console.error("获取申报订单列表失败:", error);
    orderList.value = [];
    total.value = 0;
    ElMessage.error("获取申报订单列表失败，请稍后重试");
    loading.value = false;
  });
}

/** 获取仓库列表 */
function getWarehouseOptions() {
  getWarehouseList().then(response => {
    if (response.code === "200") {
      warehouseOptions.value = response.content || [];
    } else {
      warehouseOptions.value = [];
      ElMessage.warning(response.msg || "获取仓库列表失败");
    }
  }).catch(error => {
    console.error("获取仓库列表失败:", error);
    warehouseOptions.value = [];
    ElMessage.warning("获取仓库列表失败，请稍后重试");
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  console.log("搜索按钮操作 == ",queryParams.value);
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  exportLoading.value = true;

  // 构建导出参数，使用当前搜索条件
  const exportParams = {
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate,
    warehouseId: queryParams.value.warehouseId,
    orderStatus: queryParams.value.orderStatus,
    userName: queryParams.value.userName
  };
  
console.log("导出按钮操作 == ",exportParams);
  orderListExcel(exportParams).then(response => {
    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 生成文件名
    const now = new Date();
    const dateStr = now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') + '_' +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0');

    link.download = `申报订单列表_${dateStr}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('导出成功');
  }).catch(error => {
    console.error('导出失败:', error);
    ElMessage.error('导出失败，请稍后重试');
  }).finally(() => {
    exportLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row, isEdit) {
  this.isEdit = isEdit;
  orderCode.value = row.orderCode
  orderId.value = row.id
  open.value = true
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  orderDetailData.value = [];
}

/** 获取订单详情 */
function getOrderDetail(id) {
  detailLoading.value = true;
  orderDetail({ id: id })
    .then(response => {
      if (response.code === "200") {
        const content = response.content || {};
        // 获取申报数据
        orderDetailData.value = content.appltDatas || [];
      } else {
        ElMessage.error(response.msg || "获取订单详情失败");
      }
    })
    .catch(error => {
      console.error("获取订单详情失败:", error);
      ElMessage.error("获取订单详情失败，请稍后重试");
    })
    .finally(() => {
      detailLoading.value = false;
    });
}

/** 提交表单 */
function submitForm() {
  submitLoading.value = true;

  // 构建提交数据
  const submitData = {
    id: orderId.value,
    appltDatas: orderDetailData.value.map(item => ({
      enName: item.enName,
      goodsAttr: item.goodsAttr,
      itemQuantity: item.itemQuantity,
      itemWeight: item.itemWeight
    }))
  };

  orderUpdate(submitData)
    .then(response => {
      if (response.code === "200") {
        ElMessage({
          message: "修改成功",
          type: "success"
        });
        open.value = false;
        getList(); // 刷新列表
      } else {
        ElMessage.error(response.msg || "修改失败");
      }
    })
    .catch(error => {
      console.error("修改订单失败:", error);
      ElMessage.error("修改失败，请稍后重试");
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

// 页面加载时获取数据
onMounted(() => {
  getWarehouseOptions();
  getList();
});
</script>

<style scoped>
.fixed-width-input {
  width: 220px !important;
}

/* 表单项样式 */
.el-form-item {
  margin-bottom: 20px;
  margin-right: 20px;
}

/* 多行表单样式 */
.multi-line-form {
  display: flex;
  flex-wrap: wrap;
}

/* 搜索按钮样式 */
.search-buttons {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.search-buttons .el-button {
  margin-left: 8px;
}
</style>
