<template>
  <el-dialog :title="'申报订单 - ' + orderCode" v-model="dialogVisible" width="800px" append-to-body>
    <div v-loading="loading">
      <!-- 修改表单布局，使用行内表单样式 -->
      <el-form :model="formData" label-width="100px" class="mb-12" :inline="true">
        <el-form-item label="仓库">
          <el-select v-model="formData.warehouseId" placeholder="请选择仓库" style="width: 240px;" :disabled="!isEdit">
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="formData.orderStatus" placeholder="请选择订单状态" style="width: 240px;" :disabled="!isEdit">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-form :model="formData" label-width="100px" class="mb-12" :inline="true">
        <el-form-item label="总价格">
          <el-input v-model="formData.totalPrice" disabled style="width: 240px;" />
        </el-form-item>
        <el-form-item label="总重量">
          <el-input v-model="formData.totalWeight" disabled style="width: 240px;" />
        </el-form-item>
      </el-form>

      <el-table :data="orderDetailData" border style="width: 100%">
        <el-table-column label="物品属性" align="center" prop="goodsAttr" width="220">
          <template #default="scope">
            <el-select
              v-model="scope.row.goodsAttr"
              placeholder="请选择"
              :disabled="!isEdit"
              style="width: 100%"
              popper-class="custom-select-popper"
            >
              <el-option :value="1" label="A类" />
              <el-option :value="2" label="B类" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="英文名称" align="center" prop="enName" min-width="130">
          <template #default="scope">
            <el-input v-model="scope.row.enName" placeholder="请输入" :disabled="!isEdit"/>
          </template>
        </el-table-column>
        <el-table-column label="数量" align="center" prop="itemQuantity" width="90">
          <template #default="scope">
            <el-input-number v-model="scope.row.itemQuantity" :min="1" :precision="0" :disabled="!isEdit" :controls="false"/>
          </template>
        </el-table-column>
        <el-table-column label="长(CM)" align="center" prop="chang" width="90">
          <template #default="scope">
            <el-input-number v-model="scope.row.chang" :min="1" :precision="0" :disabled="!isEdit" :controls="false"/>
          </template>
        </el-table-column>
        <el-table-column label="宽(CM)" align="center" prop="kuan" width="90">
          <template #default="scope">
            <el-input-number v-model="scope.row.kuan" :min="1" :precision="0" :disabled="!isEdit" :controls="false"/>
          </template>
        </el-table-column>
        <el-table-column label="高(CM)" align="center" prop="gao" width="90">
          <template #default="scope">
            <el-input-number v-model="scope.row.gao" :min="1" :precision="0" :disabled="!isEdit" :controls="false"/>
          </template>
        </el-table-column>
        <el-table-column label="物品总量(KG)" align="center" prop="itemWeight" width="100">
          <template #default="scope">
            <el-input-number v-model="scope.row.itemWeight" :min="0.01" :precision="2" :step="0.1" :disabled="!isEdit" :controls="false"/>
          </template>
        </el-table-column>
      </el-table>
      <!-- 添加物流信息表格 -->
      <div class="mt-4">
        <el-button type="primary" @click="addLogisticsRow" v-if="isEdit" plain>添加物流信息</el-button>
        <el-table :data="logisticsData" border style="width: 100%" class="mt-2">
          <el-table-column label="物流单号" align="center" prop="trackingNumber">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <el-input v-model="scope.row.trackingNumber" placeholder="请输入物流单号" style="width: 200px;" :disabled="!isEdit"/>
                <el-button type="primary" @click="searchLogistics(scope.row)" :loading="scope.row.searching" v-if="isEdit">
                  <el-icon><Search /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="物流公司" align="center" prop="expressCompanyId">
            <template #default="scope">
              <el-select v-model="scope.row.expressCompanyId" placeholder="请选择物流公司" :disabled="!isEdit">
                <el-option
                  v-for="item in logisticsCompanyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <el-button type="danger" link @click="removeLogisticsRow(scope.$index)" :disabled="!isEdit">
                删除
              </el-button>
            </template>
          </el-table-column>
          <template #empty>
            <div class="text-center py-4 text-gray-500">
              暂无物流信息，请点击上方按钮添加
            </div>
          </template>
        </el-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading" v-if="isEdit">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加物流信息弹窗 -->
  <el-dialog
    v-model="logisticsDialogVisible"
    title="物流信息详情"
    width="500px"
    append-to-body
  >
    <div v-if="orderContent" class="logistics-info">
      <!-- <div class="logistics-header">
        <div class="order-code">订单编号：{{ orderContent.orderCode }}</div>
        <div class="logistics-route">
          <span class="start-point">日本</span>
          <span class="status">{{ statusOptions.find(item => item.value === orderContent.orderStatus?.toString())?.label || '未知' }}</span>
          <span class="end-point">{{ orderContent.recipient.provinceCityArea.split(' ')[0] }}</span>
        </div>
        <div class="order-time">订单时间：{{ orderContent.orderTime }}</div>
      </div> -->
      <!-- 添加物流轨迹信息 -->
      <div class="logistics-timeline">
        <div v-for="(item, index) in logisticsTrackData" :key="index" class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-time">{{ item.acceptTime }}</div>
            <div class="timeline-address">{{ item.acceptAddress }}</div>
            <div class="timeline-remark">{{ item.remark }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { orderDetail, orderUpdate, getWarehouseList, queryLog } from "@/api/system/declareOrder"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: ''
  },
  orderCode: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const loading = ref(false)
const submitLoading = ref(false)
const orderDetailData = ref([])

watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    getOrderDetail()
  }
})

watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

// 表单数据
const formData = ref({
  warehouseId: '',
  orderStatus: '',
  totalPrice: '',
  totalWeight: ''
})

// 仓库选项
const warehouseOptions = ref([])

// 订单状态选项 
const statusOptions = ref([
  { value: "1001", label: "待审核" },
  { value: "1002", label: "待支付" },
  { value: "1003", label: "待收货" },
  { value: "1004", label: "已完成" }
])

// 获取仓库列表
const getWarehouses = async () => {
  try {
    const response = await getWarehouseList()
    if (response.code === "200") {
      warehouseOptions.value = response.content || []
    }
  } catch (error) {
    console.error("获取仓库列表失败:", error)
  }
}

// 获取订单详情
// 添加常量定义
const WEIGHT_LIMIT = 3  // 限重3公斤
const PRICE_A = 65     // A类价格：65元/公斤
const PRICE_B = 95     // B类价格：95元/公斤

// 计算总价和总重量
const calculateTotal = (showWarning = false) => {
  let priceVolmu = 0 // 体积重价格
  let total = 0
  let weight = 0

  orderDetailData.value.forEach(item => {
    // 计算总重量：单件重量
    const itemTotalWeight = parseFloat(item.itemWeight || 0)
    weight += itemTotalWeight

    // 根据物品属性计算价格
    const unitPrice = item.goodsAttr === 1 ? PRICE_A : PRICE_B

    // 只在需要时显示超重警告（编辑模式下用户主动修改时才提示）
    if (showWarning && itemTotalWeight > WEIGHT_LIMIT) {
      ElMessage({
        message: `${item.goodsAttr === 1 ? 'A' : 'B'}类物品超出限重${WEIGHT_LIMIT}kg`,
        type: 'warning'
      })
    }

    // 计算该物品的总价值：单价(根据重量) * 重量
    total += unitPrice * itemTotalWeight
    priceVolmu += (item.chang * item.kuan * item.gao / 6000) * unitPrice
  })

  // 如果体积重价格大于实际重价格，以体积重价格为准
  if (priceVolmu > total) {
    total = priceVolmu
  }

  // 更新表单数据
  formData.value.totalPrice = total.toFixed(2)
  formData.value.totalWeight = weight.toFixed(2)
}

// 监听物品属性和重量的变化
watch(orderDetailData, () => {
  // 只在编辑模式下显示警告
  calculateTotal()
}, { deep: true })

// 添加全局订单详情数据
const orderContent = ref(null)

// 修改获取订单详情方法
const getOrderDetail = async () => {
  if (!props.orderId) return
  loading.value = true
  try {
    const response = await orderDetail({ id: props.orderId })
    if (response.code === "200") {
      const content = response.content || {}
      // 保存全局数据
      orderContent.value = content
      orderDetailData.value = content.appltDatas || []
      // 设置表单数据
      formData.value = {
        warehouseId: content.warehouseId,
        orderStatus: content.orderStatus?.toString(),
        totalPrice: content.totalPrice,
        totalWeight: content.totalWeight
      }
      // 初始计算总价和重量
      calculateTotal(true)
      
      // 设置物流信息
      logisticsData.value = content.logistics || []
    } else {
      ElMessage.error(response.msg || "获取订单详情失败")
    }
  } catch (error) {
    ElMessage.error("获取订单详情失败，请稍后重试")
  } finally {
    loading.value = false
  }
}

// 添加物流弹窗控制变量
const logisticsDialogVisible = ref(false)

// 添加物流轨迹数据
const logisticsTrackData = ref([])

// 修改搜索物流信息方法
const searchLogistics = async (row) => {
  if (!orderContent.value) {
    ElMessage.warning('订单数据不存在')
    return
  }
  if (!row.trackingNumber) {
    ElMessage.warning('请输入物流单号')
    return
  }
  if (!row.expressCompanyId) {
    ElMessage.warning('请选择物流公司')
    return
  }

  row.searching = true
  try {
    // 调用物流查询接口
    const response = await queryLog({ 
      expressCompanyId: row.expressCompanyId,
      trackingNumber: row.trackingNumber
    })
    if (response.code === "200") {
      logisticsTrackData.value = response.content || []
      logisticsDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || "获取物流信息失败")
    }
  } catch (error) {
    ElMessage.error('物流信息查询失败')
  } finally {
    row.searching = false
  }
}

// 提交表单
const submitForm = async () => {
  submitLoading.value = true
  try {
    const submitData = {
      id: props.orderId,
      warehouseId: formData.value.warehouseId,
      orderStatus: formData.value.orderStatus,
      appltDatas: orderDetailData.value.map(item => ({
        enName: item.enName,
        goodsAttr: item.goodsAttr,
        itemQuantity: item.itemQuantity,
        itemWeight: item.itemWeight,
        chang: item.chang,
        kuan: item.kuan,
        gao: item.gao
      })),
      logistics: logisticsData.value // 添加物流信息
    }

    const response = await orderUpdate(submitData)
    if (response.code === "200") {
      ElMessage({
        message: "修改成功",
        type: "success"
      })
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error(response.msg || "修改失败")
    }
  } catch (error) {
    console.error("修改订单失败:", error)
    ElMessage.error("修改失败，请稍后重试")
  } finally {
    submitLoading.value = false
  }
}

// 组件挂载时获取仓库列表
onMounted(() => {
  getWarehouses()
})

const handleClose = () => {
  dialogVisible.value = false
  orderDetailData.value = []
  logisticsData.value = [] // 清空物流数据
}

// 物流公司选项
const logisticsCompanyOptions = [
  { value: 1, label: '国际中通' },
  { value: 2, label: '顺丰快递' },
  { value: 3, label: '中通快递' },
  { value: 4, label: '安能快递' }
]

// 物流信息数据
const logisticsData = ref([])

// 修改添加物流行方法
const addLogisticsRow = () => {
  logisticsData.value.push({
    id: 0,
    trackingNumber: '', // 快递号
    expressCompanyId: '', // 物流公司ID
    orderId: props.orderId, // 订单ID
  })
}

// 删除物流行
const removeLogisticsRow = (index) => {
  logisticsData.value.splice(index, 1)
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
.w-full {
  width: 100%;
}
.mt-4 {
  margin-top: 16px;
}
.mt-2 {
  margin-top: 8px;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.text-center {
  text-align: center;
}
.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.text-gray-500 {
  color: #6b7280;
}

/* 物流信息样式 */
.logistics-info {
  padding: 16px;
}

.logistics-header {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.order-code {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
}

.logistics-route {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0;
  position: relative;
  padding: 0 20px;
}

.logistics-route::before {
  content: '';
  position: absolute;
  left: 50px;
  right: 50px;
  top: 50%;
  height: 1px;
  background: #dcdfe6;
  z-index: 1;
}

.start-point,
.end-point {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  background: #fff;
  padding: 0 10px;
  z-index: 2;
}

.status {
  font-size: 14px;
  color: #409eff;
  background: #fff;
  padding: 0 10px;
  z-index: 2;
}

.order-time {
  font-size: 14px;
  color: #666;
  margin-top: 12px;
}

/* 物流轨迹样式 */
.logistics-timeline {
  /* margin-top: 20px; */
  padding: 0 20px;
}

.timeline-item {
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 5px;
  top: 24px;
  bottom: 0;
  width: 1px;
  background: #e4e7ed;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #409eff;
}

.timeline-content {
  padding-left: 15px;
}

.timeline-time {
  font-size: 14px;
  color: #409eff;
  margin-bottom: 4px;
}

.timeline-address {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.timeline-remark {
  font-size: 14px;
  color: #666;
}

/* 最后一个时间线项目不显示连接线 */
.timeline-item:last-child::before {
  display: none;
}

/* 强制限制 el-select 下拉菜单宽度 */
.custom-select-popper {
  max-width: 130px !important;
  min-width: 130px !important;
  width: 130px !important; /* 明确指定宽度 */
}

.custom-select-popper .el-select-dropdown__item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10px; /* 调整内边距以适应宽度 */
}
</style>

