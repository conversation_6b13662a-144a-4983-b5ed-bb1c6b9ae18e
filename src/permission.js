import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp, isPathMatch } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register']

const isWhiteList = (path) => {
  return whiteList.some(pattern => isPathMatch(pattern, path))
}

router.beforeEach((to, from, next) => {
  NProgress.start()
  // 设置页面标题（无论是否有token都设置）
  to.meta.title && useSettingsStore().setTitle(to.meta.title)
  if (getToken()) {
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/system/declareOrder' })
      NProgress.done()
    } else if (isWhiteList(to.path)) {
      next()
    } else {
      // 检查用户是否已有角色信息
      const userStore = useUserStore()
      const permissionStore = usePermissionStore()

      // 如果用户角色信息为空，尝试从本地存储恢复
      if (userStore.roles.length === 0) {
        const restored = userStore.restoreUserInfo()
        if (!restored) {
          // 如果无法恢复用户信息，重定向到登录页
          console.log('无法恢复用户信息，重定向到登录页')
          next({ path: '/login' })
          NProgress.done()
          return
        }
      }

      // 检查路由是否已经添加
      const hasRoutes = permissionStore.routes.length > 0

      // 检查当前路由是否存在
      const routeExists = router.hasRoute(to.name)

      // 如果路由未添加或当前路由不存在，则重新生成路由
      if (!hasRoutes || !routeExists) {
        // 重新生成路由
        permissionStore.generateRoutes().then(accessRoutes => {
          // 清除可能存在的重复路由
          accessRoutes.forEach(route => {
            if (router.hasRoute(route.name)) {
              router.removeRoute(route.name)
            }
          })

          // 添加路由
          accessRoutes.forEach(route => {
            if (!isHttp(route.path)) {
              router.addRoute(route) // 动态添加可访问路由表
            }
          })

          // 使用replace: true，确保导航不会留下历史记录
          next({ ...to, replace: true })
        })
      } else {
        // 路由已添加，直接放行
        next()
      }
    }
  } else {
    // 没有token
    if (isWhiteList(to.path)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
