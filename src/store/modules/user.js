import { login, logout } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { isHttp, isEmpty } from "@/utils/validate"
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      id: '',
      name: '',
      avatar: defAva,
      roles: [],
      permissions: [],
      type: null // 添加用户类型字段
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        return new Promise((resolve, reject) => {
          login(username, password).then(res => {
            const content = res.content
            setToken(content.token)
            this.token = content.token
            this.id = content.id
            this.name = content.name
            this.type = content.type // 保存用户类型

            // 直接设置权限，不再从后端获取
            this.permissions = ["*:*:*"] // 所有权限

            // 根据角色设置 1: 系统管理员 2:仓库管理员
            this.roles = []
            console.log('用户类型:', content.type)
            if (content.type == 1) {
              this.roles.push('admin')  // 系统管理员角色
              console.log('设置为系统管理员角色')
            } else if (content.type == 2) {
              this.roles.push('warehouse')  // 仓库管理员角色
              console.log('设置为仓库管理员角色')
            }
            console.log('用户角色:', this.roles)

            // 将用户信息保存到本地存储
            localStorage.setItem('userInfo', JSON.stringify({
              id: content.id,
              name: content.name,
              type: content.type,
              roles: this.roles,
              permissions: this.permissions
            }))

            // 清除本地存储的路由缓存
            localStorage.removeItem('sidebarRouters')

            // 预先生成路由，避免导航守卫中的重定向问题
            import('@/store/modules/permission').then(module => {
              const permissionStore = module.default()
              permissionStore.generateRoutes()
            })

            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            this.type = null
            removeToken()
            // 清除本地存储的用户信息
            localStorage.removeItem('userInfo')
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 从本地存储恢复用户信息
      restoreUserInfo() {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          try {
            const parsed = JSON.parse(userInfo)
            this.id = parsed.id || ''
            this.name = parsed.name || ''
            this.type = parsed.type || null
            this.roles = parsed.roles || []
            this.permissions = parsed.permissions || []
            console.log('从本地存储恢复用户信息:', parsed)
            return true
          } catch (error) {
            console.error('解析本地用户信息失败:', error)
            localStorage.removeItem('userInfo')
            return false
          }
        }
        return false
      }
    }
  })

export default useUserStore
